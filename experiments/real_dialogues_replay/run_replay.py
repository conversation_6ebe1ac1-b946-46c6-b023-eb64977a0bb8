#!/usr/bin/env python3
"""
真实对话重放（Replay）最小可用版本

目标：
- 读取 real_dialogues 下的原始文档与对话记录（按文件名线索关联）
- 使用默认“语义分割”配置准备文档切片（占位实现）
- 每轮：基于用户发言进行检索（默认 Manticore，占位为本地相似度），构建上下文，调用真实 LLM（聚合提供商，按 MODEL 切换）生成回答预览
- 生成基于 langextract 的“双边摘要”结构（占位：保持字段位形，标注为mock，可后续接 langextract）
- 输出 results/sessions/<session_id>/round_*.json 与 replay_report.md（简要）

说明：
- 本脚本为最小可用打通版本，不依赖网络时将退化为本地相似度与mock摘要，但保留相同输出结构；
- 当 .env 配好并可访问聚合 LLM 时，使用 MODEL 环境变量（如 gpt-5-nano / gemini-2.5-pro）进行真实生成。
"""

import os
import re
import json
import time
import glob
import argparse
import requests
from dataclasses import dataclass, asdict
from typing import List, Dict, Any, Optional

from dotenv import load_dotenv
import math
try:
    import numpy as np
except Exception:
    np = None

# 可选：Manticore 官方 Python 客户端
try:
    import manticoresearch  # type: ignore
    MANTICORE_CLIENT_AVAILABLE = True
except Exception:
    MANTICORE_CLIENT_AVAILABLE = False

ROOT = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
DEFAULT_CORPUS_DIR = os.path.join(ROOT, 'real_dialogues')
RESULTS_DIR = os.path.join(ROOT, 'real_dialogues_replay', 'results')

os.makedirs(RESULTS_DIR, exist_ok=True)
load_dotenv()
# 同时尝试加载 experiments/.env（确保从仓库内的配置生效）
load_dotenv(os.path.join(ROOT, '.env'))

# 可选：langextract（结构化抽取）
try:
    import langextract as lx  # type: ignore
    LANGEXTRACT_AVAILABLE = True
except Exception:
    LANGEXTRACT_AVAILABLE = False


# ------------------------- 基础工具 -------------------------

def read_file(path: str) -> str:
    with open(path, 'r', encoding='utf-8') as f:
        return f.read()


def extract_turns_from_dialogue(md_text: str) -> List[Dict[str, Any]]:
    """从对话 Markdown 提取轮次（解析 user/assistant 全量），保持顺序。"""
    blocks = re.split(r"\n---\n", md_text)
    turns = []
    turn_id = 0
    for b in blocks:
        if '## 🧑‍💻 User' in b:
            content = b.split('## 🧑‍💻 User', 1)[-1].strip()
            content = re.sub(r'^#+.*\n', '', content).strip()
            turn_id += 1
            turns.append({"turn_id": turn_id, "role": 'user', "text": content})
        elif '## 🤖 Assistant' in b:
            content = b.split('## 🤖 Assistant', 1)[-1].strip()
            content = re.sub(r'^#+.*\n', '', content).strip()
            # assistant 回合不递增 turn_id，绑定上一个 user 回合
            turns.append({"turn_id": turn_id, "role": 'assistant', "text": content})
    return [t for t in turns if t.get('text')]


def split_sentences(text: str) -> List[Dict[str, Any]]:
    """基于中文句读的简单句子切分，返回字典含句子与起止位。"""
    sentences: List[Dict[str, Any]] = []
    pattern = re.compile(r'([^。！？\n\r]+[。！？]?)')
    pos = 0
    for m in pattern.finditer(text):
        sent = m.group(1)
        if not sent:
            continue
        start = m.start(1)
        end = m.end(1)
        sentences.append({"text": sent, "start": start, "end": end})
        pos = end
    if pos < len(text):
        tail = text[pos:]
        if tail.strip():
            sentences.append({"text": tail, "start": pos, "end": len(text)})
    return sentences


def semantic_split(text: str, target_size: int = 900, max_size: int = 1200) -> List[Dict[str, Any]]:
    """仿 E2 的语义分割：按句子累加到 target_size，越界或超过 max_size 时切片。"""
    sents = split_sentences(text)
    chunks: List[Dict[str, Any]] = []
    cur: List[Dict[str, Any]] = []
    cur_len = 0
    cur_start = 0
    cid = 0
    for seg in sents:
        if not cur:
            cur_start = seg['start']
        seg_len = len(seg['text'])
        if cur_len + seg_len > target_size and cur_len > 0:
            content = ''.join([x['text'] for x in cur]).strip()
            chunks.append({
                'id': f'chunk_{cid}',
                'content': content,
                'start': cur_start,
                'end': cur_start + len(content)
            })
            cid += 1
            cur = [seg]
            cur_start = seg['start']
            cur_len = seg_len
        else:
            cur.append(seg)
            cur_len += seg_len
        if cur_len > max_size:
            content = ''.join([x['text'] for x in cur]).strip()
            chunks.append({
                'id': f'chunk_{cid}',
                'content': content,
                'start': cur_start,
                'end': cur_start + len(content)
            })
            cid += 1
            cur = []
            cur_len = 0
    if cur:
        content = ''.join([x['text'] for x in cur]).strip()
        chunks.append({
            'id': f'chunk_{cid}',
            'content': content,
            'start': cur_start,
            'end': cur_start + len(content)
        })
    return chunks


def _cosine(a, b):
    if np is None:
        return 0.0
    an = np.linalg.norm(a) + 1e-8
    bn = np.linalg.norm(b) + 1e-8
    return float(np.dot(a, b) / (an * bn))


def _embed_openai(texts: List[str], model: str) -> Optional[List[List[float]]]:
    try:
        import openai
        client = openai.OpenAI(api_key=os.getenv('OPENAI_API_KEY'), base_url=os.getenv('OPENAI_BASE_URL'))
        res = client.embeddings.create(model=model, input=texts)
        return [d.embedding for d in res.data]
    except Exception:
        return None


def _embed_local(texts: List[str]) -> Optional[List[List[float]]]:
    try:
        from sentence_transformers import SentenceTransformer
        model_name = os.getenv('LOCAL_EMBED_MODEL', 'all-MiniLM-L6-v2')
        m = SentenceTransformer(model_name)
        vecs = m.encode(texts, show_progress_bar=False)
        return [v.tolist() for v in vecs]
    except Exception:
        return None


def vector_retrieval(query: str, chunks: List[Dict[str, Any]], top_k: int = 3) -> List[Dict[str, Any]]:
    """向量检索（优先聚合OpenAI兼容的 embeddings，回退本地，最终回退字符重叠）。"""
    # 若指定使用 Manticore，则尝试走 Manticore HTTP 路径
    if os.getenv('SEARCH_ENGINE', 'manticore').lower() == 'manticore':
        try:
            return vector_retrieval_manticore(query, chunks, top_k=top_k)
        except Exception as e:
            # 失败则回退到本地/开放接口路径
            pass
    texts = [c['content'] for c in chunks]
    embed_model = os.getenv('EMBED_MODEL', 'text-embedding-3-small')
    # 文档向量
    doc_vecs = _embed_openai(texts, embed_model) or _embed_local(texts)
    if doc_vecs and np is not None:
        # 查询向量
        qv_list = _embed_openai([query], embed_model) or _embed_local([query])
        if qv_list:
            qv = np.array(qv_list[0], dtype=float)
            scored = []
            for ch, v in zip(chunks, doc_vecs):
                s = _cosine(qv, np.array(v, dtype=float))
                scored.append((s, ch))
            scored.sort(key=lambda x: x[0], reverse=True)
            out = []
            for s, ch in scored[:top_k]:
                out.append({
                    "chunk_id": ch['id'],
                    "doc_id": "source_doc",
                    "offsets": [ch['start'], ch['end']],
                    "score": float(s),
                    "preview": ch['content'][:200]
                })
            return out

    # 最终回退：字符重叠
    def score(q: str, c: str) -> float:
        qset = set(q)
        cset = set(c)
        inter = len(qset & cset)
        return inter / (len(qset) + 1e-6)
    scored = []
    for ch in chunks:
        s = score(query, ch['content'])
        scored.append((s, ch))
    scored.sort(key=lambda x: x[0], reverse=True)
    out = []
    for s, ch in scored[:top_k]:
        out.append({
            "chunk_id": ch['id'],
            "doc_id": "source_doc",
            "offsets": [ch['start'], ch['end']],
            "score": float(s),
            "preview": ch['content'][:200]
        })
    return out


def _manticore_sql(sql: str) -> Dict[str, Any]:
    host = os.getenv('MANTICORE_HOST', 'localhost')
    port = int(os.getenv('MANTICORE_HTTP_PORT', os.getenv('MANTICORE_PORT', '9308')))
    url = f"http://{host}:{port}/sql"
    r = requests.post(url, json={"query": sql})
    r.raise_for_status()
    return r.json()


def vector_retrieval_manticore(query: str, chunks: List[Dict[str, Any]], top_k: int = 3) -> List[Dict[str, Any]]:
    """使用 Manticore 官方 Python 客户端构建临时向量表并进行 KNN 检索。"""
    if not MANTICORE_CLIENT_AVAILABLE:
        raise RuntimeError('manticoresearch 客户端不可用')

    embed_model = os.getenv('EMBED_MODEL', 'text-embedding-3-small')
    texts = [c['content'] for c in chunks]
    vecs = _embed_openai(texts, embed_model) or _embed_local(texts)
    if not vecs:
        raise RuntimeError('无法生成嵌入，放弃 Manticore 路径')
    dim = len(vecs[0])

    host = os.getenv('MANTICORE_HOST', 'localhost')
    port = int(os.getenv('MANTICORE_HTTP_PORT', os.getenv('MANTICORE_PORT', '9308')))

    configuration = manticoresearch.Configuration(host=f"http://{host}:{port}")
    api_client = manticoresearch.ApiClient(configuration)
    utils_api = manticoresearch.UtilsApi(api_client)
    search_api = manticoresearch.SearchApi(api_client)
    index_api = manticoresearch.IndexApi(api_client)

    table = os.getenv('MANTICORE_TABLE', 'replay_chunks')
    # 清表并重建
    try:
        utils_api.sql(f"DROP TABLE IF EXISTS {table}")
    except Exception:
        pass
    utils_api.sql(
        f"""
        CREATE TABLE {table} (
            chunk_id string,
            start int,
            end int,
            content text,
            embedding float_vector knn_type='hnsw' knn_dims='{dim}' hnsw_similarity='cosine'
        )
        """
    )

    # 索引全部切片
    for ch, v in zip(chunks, vecs):
        doc = {
            'chunk_id': ch['id'],
            'start': int(ch['start']),
            'end': int(ch['end']),
            'content': ch['content'][:5000],
            'embedding': v
        }
        index_api.insert({'table': table, 'doc': doc})

    # 查询向量
    qv_list = _embed_openai([query], embed_model) or _embed_local([query])
    if not qv_list:
        raise RuntimeError('无法为查询生成嵌入')
    qv = qv_list[0]

    # KNN 检索
    search_request = {
        'table': table,
        'knn': {
            'field': 'embedding',
            'query_vector': qv,
            'k': top_k
        }
    }
    response = search_api.search(search_request)

    out: List[Dict[str, Any]] = []
    hits = getattr(response, 'hits', None)
    if hits:
        for hit in hits:
            src = getattr(hit, '_source', None) or {}
            # 根据 chunk_id 反查 start/end
            cid = src.get('chunk_id')
            ch = next((c for c in chunks if c['id'] == cid), None)
            if not ch:
                continue
            out.append({
                'chunk_id': ch['id'],
                'doc_id': 'source_doc',
                'offsets': [ch['start'], ch['end']],
                'score': 0.0,  # Manticore 不直接返回相似度分数，先占位
                'preview': ch['content'][:200]
            })
    return out


def call_llm(prompt: str) -> str:
    """调用聚合 LLM（OpenAI 兼容协议）。若不可用，返回占位生成。"""
    try:
        import openai
        client = openai.OpenAI(
            api_key=os.getenv('OPENAI_API_KEY'),
            base_url=os.getenv('OPENAI_BASE_URL')
        )
        model = os.getenv('MODEL', 'gpt-5-nano')
        resp = client.chat.completions.create(
            model=model,
            messages=[{"role": "user", "content": prompt}],
            temperature=0.3,
        )
        return resp.choices[0].message.content
    except Exception as e:
        return f"[mock-answer due to LLM error: {e}] 答案预览占位。"


def llm_json_summary(user_text: str, answer_text: str, retrieved: List[Dict[str, Any]]) -> Dict[str, Any]:
    """使用 LLM 生成符合 schema 的摘要；生成失败时回退最小结构。"""
    system = (
        "你是一个摘要器。请输出 JSON，不要包含多余文字。"
        "格式: {\"user\":string,\"assistant\":string,\"intent\":string,\"key_points\":[],\"anchors\":[]}"
    )
    user = (
        f"用户原话: {user_text}\n助手回答: {answer_text}\n"
        "请给出简洁的 user 与 assistant 摘要，intent 为 1-3 个字的意图（如\"入门\"、\"释疑\"），"
        "key_points 为要点短句数组。anchors 留空数组（由外部填充）。"
    )
    try:
        import openai
        client = openai.OpenAI(api_key=os.getenv('OPENAI_API_KEY'), base_url=os.getenv('OPENAI_BASE_URL'))
        # 后台任务使用 INTENT_MODEL，默认回落到 MODEL，再回落到 gpt-5-nano
        model = os.getenv('INTENT_MODEL', os.getenv('MODEL', 'gpt-5-nano'))
        resp = client.chat.completions.create(
            model=model,
            messages=[{"role": "system", "content": system}, {"role": "user", "content": user}],
            temperature=0
        )
        import json as _json
        txt = resp.choices[0].message.content
        data = _json.loads(txt)
    except Exception:
        data = {
            'user': user_text[:200],
            'assistant': answer_text[:200],
            'intent': 'general',
            'key_points': []
        }
    # 填充 anchors
    data['anchors'] = [
        {"chunk_id": r['chunk_id'], "doc_id": r['doc_id'], "offsets": r['offsets'], "confidence": 0.5}
        for r in retrieved
    ]
    return data


def try_langextract_summary(user_text: str, answer_text: str, retrieved: List[Dict[str, Any]]) -> Optional[Dict[str, Any]]:
    """当 EXTRACTOR=langextract 且可用时，尝试用 langextract 生成结构化摘要；失败返回 None。

    说明：anchors 仍由检索命中填充；intent/key_points 基于抽取结果简化映射。
    """
    if os.getenv('EXTRACTOR', '').lower() != 'langextract' or not LANGEXTRACT_AVAILABLE:
        return None
    try:
        text = f"用户: {user_text}\nAI: {answer_text}"
        prompt_description = (
            "从这一次用户与AI的对话回合中提取：\n"
            "- dialogue_turn: speaker(user/ai)、intent(中文1-3字)、topic(可选)\n"
            "- key_concept: 对话中的关键信息点（短语）\n"
            "请确保 extraction_text 为原文片段。"
        )
        model_id = os.getenv('LX_MODEL', 'gemini-2.5-flash')
        doc = lx.extract(text_or_documents=text, prompt_description=prompt_description, model_id=model_id)
        intent = 'general'
        key_points: List[str] = []
        if doc and getattr(doc, 'extractions', None):
            for ext in doc.extractions:
                cls = getattr(ext, 'extraction_class', '')
                attrs = getattr(ext, 'attributes', {}) or {}
                if cls == 'dialogue_turn' and isinstance(attrs, dict):
                    if attrs.get('speaker') in ('user', 'ai') and attrs.get('intent'):
                        # 取 user 的意图优先
                        if attrs.get('speaker') == 'user':
                            intent = str(attrs.get('intent'))
                elif cls == 'key_concept':
                    if getattr(ext, 'extraction_text', None):
                        key_points.append(str(ext.extraction_text))
                    elif isinstance(attrs, dict) and attrs.get('concept'):
                        key_points.append(str(attrs['concept']))
        data = {
            'user': user_text[:200],
            'assistant': answer_text[:200],
            'intent': intent or 'general',
            'key_points': key_points[:8],
            'anchors': [
                {"chunk_id": r['chunk_id'], "doc_id": r['doc_id'], "offsets": r['offsets'], "confidence": 0.5}
                for r in retrieved
            ]
        }
        return data
    except Exception:
        return None


# ------------------------- 主流程 -------------------------

def run(args):
    # 读取原始文档（以《大脑健身房》为样例）
    doc_candidates = glob.glob(os.path.join(args.corpus_dir, "*大脑健身房*解读*.md"))
    if not doc_candidates:
        raise FileNotFoundError("未找到原始文档（匹配 *大脑健身房*解读*.md），请确认 experiments/real_dialogues 目录")
    source_doc_path = doc_candidates[0]
    source_text = read_file(source_doc_path)

    # 读取对话记录（以“大脑健身入门指导*.md”为样例）
    dlg_candidates = glob.glob(os.path.join(args.corpus_dir, "大脑健身入门指导*.md"))
    if not dlg_candidates:
        raise FileNotFoundError("未找到对话记录（匹配 大脑健身入门指导*.md），请确认 experiments/real_dialogues 目录")
    dialogue_path = dlg_candidates[0]
    dialogue_text = read_file(dialogue_path)

    # 切分文档（语义分割占位）
    # 语义分割（E2 风格）
    chunks = semantic_split(source_text, target_size=args.target_size, max_size=args.max_size)

    # 读取 persona
    persona_path = os.path.join(args.corpus_dir, 'persona-prompt.md')
    persona_text = read_file(persona_path) if os.path.exists(persona_path) else ''

    # 解析轮次（完整），仅以 user 触发 replay
    all_turns = extract_turns_from_dialogue(dialogue_text)
    user_turns = [t for t in all_turns if t['role'] == 'user']

    # 运行目录（避免覆盖）：runs/<RUN_NAME>/
    ts = time.strftime('%Y%m%d-%H%M%S')
    default_run_name = f"{os.getenv('MODEL', 'gpt-5-nano')}-{ts}"
    safe_run_name = (args.run_name or default_run_name).replace('/', '-').replace(' ', '-')
    run_dir = os.path.join(RESULTS_DIR, 'runs', safe_run_name)
    os.makedirs(run_dir, exist_ok=True)

    # 会话结果目录
    session_id = os.path.splitext(os.path.basename(dialogue_path))[0]
    session_dir = os.path.join(run_dir, 'sessions', session_id)
    os.makedirs(session_dir, exist_ok=True)

    # 报告头部元信息
    report_lines = [
        f"# 重放报告 - {session_id}",
        "",
        "## 运行信息",
        f"- Run name: {safe_run_name}",
        f"- Timestamp: {ts}",
        f"- Model: {os.getenv('MODEL', 'gpt-5-nano')}",
        f"- Intent model: {os.getenv('INTENT_MODEL', os.getenv('MODEL', 'gpt-5-nano'))}",
        f"- Extractor: {os.getenv('EXTRACTOR', 'llm-json')} (langextract_available={LANGEXTRACT_AVAILABLE})",
        f"- LX model: {os.getenv('LX_MODEL', 'gemini-2.5-flash')}",
        f"- Embed model: {os.getenv('EMBED_MODEL', 'text-embedding-3-small')}",
        f"- Search engine: {os.getenv('SEARCH_ENGINE', 'manticore')}",
        f"- 源文档: {os.path.basename(source_doc_path)}",
        f"- 对话记录: {os.path.basename(dialogue_path)}",
        ""
    ]

    history: List[Dict[str, str]] = []  # 存储我们生成的对话历史
    rounds_done = 0
    for t in user_turns:
        if args.max_rounds and rounds_done >= args.max_rounds:
            break
        q = t['text']
        retrieved = vector_retrieval(q, chunks, top_k=args.top_k)
        # 拼装上下文（简化：拼接 TopK 片段）
        stitched_context = "\n\n".join([r['preview'] for r in retrieved])

        # 组装短期记忆（最近 N 轮）
        recent = history[-args.max_history:]
        mem_text = "\n".join([f"{h['role']}: {h['content']}" for h in recent]) if recent else ""

        # Persona + 记忆 + 材料 + 问题
        prompt_parts = []
        if persona_text:
            prompt_parts.append(f"[Persona]\n{persona_text.strip()}\n")
        if mem_text:
            prompt_parts.append(f"[Conversation Memory]\n{mem_text}\n")
        prompt_parts.append(f"[Materials]\n{stitched_context}\n")
        prompt_parts.append(f"[Question]\n{q}\n")
        prompt_parts.append("要求：基于材料与记忆，用简洁中文回答，必要时指出依据来自文档片段或常识。")
        prompt = "\n".join(prompt_parts)
        answer_preview = call_llm(prompt)
        summary = try_langextract_summary(q, answer_preview, retrieved) or \
                  llm_json_summary(q, answer_preview, retrieved)

        round_obj = {
            "turn_id": t['turn_id'],
            "user_text": q,
            "retrieved_chunks": retrieved,
            "stitched_ctx_tokens": len(stitched_context),  # 占位，用字符长度近似
            "answer_preview": answer_preview,
            "summary": summary
        }

        round_path = os.path.join(session_dir, f"round_{t['turn_id']:03d}.json")
        with open(round_path, 'w', encoding='utf-8') as f:
            json.dump(round_obj, f, ensure_ascii=False, indent=2)

        report_lines.append(f"## Round {t['turn_id']}")
        report_lines.append(f"- Question: {q[:100]}")
        report_lines.append(f"- Retrieved: {len(retrieved)} chunks, top1_score={retrieved[0]['score'] if retrieved else 0:.3f}")
        cleaned_preview = answer_preview[:160].replace("\n", " ")
        report_lines.append(f"- Answer preview: {cleaned_preview}")
        report_lines.append("")

        # 更新历史
        history.append({"role": "user", "content": q})
        history.append({"role": "assistant", "content": answer_preview})
        rounds_done += 1

    # 写入报告
    report_path = os.path.join(run_dir, 'replay_report.md')
    with open(report_path, 'w', encoding='utf-8') as f:
        f.write("\n".join(report_lines))


def build_arg_parser() -> argparse.ArgumentParser:
    p = argparse.ArgumentParser(description='真实对话重放（Replay）')
    p.add_argument('--corpus_dir', default=DEFAULT_CORPUS_DIR, help='语料目录（原始文档与对话）')
    p.add_argument('--target_size', type=int, default=900, help='语义分割目标长度')
    p.add_argument('--max_size', type=int, default=1200, help='语义分割最大长度')
    p.add_argument('--top_k', type=int, default=3, help='检索 TopK')
    p.add_argument('--max_rounds', type=int, default=6, help='最多重放轮数（user 触发）')
    p.add_argument('--max_history', type=int, default=2, help='短期记忆窗口（最近轮数）')
    p.add_argument('--run_name', type=str, default=None, help='运行名（默认 MODEL-时间戳）')
    return p


if __name__ == '__main__':
    parser = build_arg_parser()
    args = parser.parse_args()
    run(args)

    # 这些变量在 run() 函数内部定义，需要重新计算用于输出
    ts = time.strftime('%Y%m%d-%H%M%S')
    default_run_name = f"{os.getenv('MODEL', 'gpt-5-nano')}-{ts}"
    safe_run_name = (args.run_name or default_run_name).replace('/', '-').replace(' ', '-')
    run_dir = os.path.join(RESULTS_DIR, 'runs', safe_run_name)

    # 读取对话记录以获取 session_id
    dlg_candidates = glob.glob(os.path.join(args.corpus_dir, "大脑健身入门指导*.md"))
    if dlg_candidates:
        dialogue_path = dlg_candidates[0]
        session_id = os.path.splitext(os.path.basename(dialogue_path))[0]
        session_dir = os.path.join(run_dir, 'sessions', session_id)
        report_path = os.path.join(run_dir, 'replay_report.md')

        print(f"重放完成。会话轮次输出目录: {session_dir}")
        print(f"报告: {report_path}")
    else:
        print("重放完成。")
