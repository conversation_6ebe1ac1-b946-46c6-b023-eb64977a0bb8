# 实验：真实对话重放（Replay）与检索拼装可观测性

目标：基于 `experiments/real_dialogues/` 的真实资料，按 PRD v1.1 的“典型用户流程”重放端到端对话，逐轮记录：
- 检索到的原文片段（`doc_id/chunk_id/offsets`）
- 拼接后的上下文（短期记忆 + 语义锚点 + 文档 TopK）
- 每一轮生成的“双边摘要”（`speaker/intent/key_points/anchors[]`）

已对齐的选型：
- 分割：默认“语义分割”（朴素仅作回退/对照）
- 检索：默认 Manticore（pgvector 作为回退）
- 抽取：默认 `langextract`（规则法作回退）

模型使用策略：
- 基础重放（成本优先）：使用真实在线模型 `gpt-5-nano` 作为低成本模型进行真实生成与摘要；
- 实际对比（风格对齐）：技术链路跑通后，切换到 `gemini-2.5-pro` 进行同样的重放，对比对话风格与引用效果。
 说明：LLM 提供商为聚合服务，通过 `.env` 指定统一的 API（OpenAI 兼容）；仅需切换 `MODEL` 即可，无需切换 Provider。

目录结构
```
experiments/real_dialogues_replay/
  README.md
  run_replay.py            # 重放主脚本（后续补充）
  config.yaml              # TopK/窗口/引擎/模型等参数（后续补充）
  results/
    runs/
      <RUN_NAME>/
        sessions/
          <session_id>/
            round_001.json     # 每轮：检索片段/上下文/答案预览/摘要/anchors
            round_002.json
        replay_report.md       # 汇总报告（逐轮可读对照）
```

运行说明（占位，待脚本落地）
- 基础重放（低成本真实生成）：
  `MODEL=gpt-5-nano EMBED_MODEL=text-embedding-3-small SEARCH_ENGINE=manticore python experiments/real_dialogues_replay/run_replay.py --max_rounds 6 --max_history 2`
- Gemini 对比（独立 run 目录）：
  `MODEL=gemini-2.5-pro EMBED_MODEL=text-embedding-3-small SEARCH_ENGINE=manticore python experiments/real_dialogues_replay/run_replay.py --max_rounds 6 --max_history 2`
  或自定义名称避免混淆：`--run_name gemini-pro-replay-01`

环境变量策略（成本/质量分工）
- INTENT_MODEL（默认 `gpt-5-nano`）：意图识别、摘要等“后台任务”；
- MODEL（例如 `gemini-2.5-pro`）：用户可见回答；
- LX_MODEL（例如 `gemini-2.5-flash`）：langextract 抽取模型；
- EXTRACTOR：设置为 `langextract` 时启用 langextract；否则回退为小模型 JSON 摘要。

输出与指标
- 逐轮字段：`turn_id, user_text, retrieved_chunks[], stitched_ctx_tokens, answer_preview, summary{user,assistant,anchors[]}`
- 指标：检索覆盖率、跨标题污染率、上下文预算、anchors 命中率、端到端延迟（可选）

备注
- 重放脚本复用现有能力：检索（E1 Provider 抽象）、切分（E2 语义分割默认）、摘要/anchors（E3 `langextract`）。
- 基线/回退：`MODEL=gpt-5-nano` 为基础重放默认；网络/凭证不可用时允许本地仿真回退，仅用于流水线打通，不计入对比结论。
