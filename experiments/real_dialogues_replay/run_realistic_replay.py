#!/usr/bin/env python3
"""
真实对话重放（Realistic Replay）- 改进版本

改进点：
1. 真实对话逻辑：AI 根据上一轮的回答和用户问题进行真实回复，而不是简单重放原始问题
2. 早期失败检测：一旦某轮出现问题，立即停止，不继续后续轮次
3. 真实技术栈运行：确保 Manticore、向量检索等后台技术真实运行
4. 智能用户模拟：基于上下文生成更自然的用户回复
"""

import os
import re
import json
import time
import glob
import argparse
import requests
from dataclasses import dataclass, asdict
from typing import List, Dict, Any, Optional

from dotenv import load_dotenv
import math
try:
    import numpy as np
except Exception:
    np = None

# Manticore 客户端
try:
    import manticoresearch
    MANTICORE_CLIENT_AVAILABLE = True
except Exception:
    MANTICORE_CLIENT_AVAILABLE = False

ROOT = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
DEFAULT_CORPUS_DIR = os.path.join(ROOT, 'real_dialogues')
RESULTS_DIR = os.path.join(ROOT, 'real_dialogues_replay', 'results')

os.makedirs(RESULTS_DIR, exist_ok=True)
load_dotenv()
load_dotenv(os.path.join(ROOT, '.env'))

# 可选：langextract（结构化抽取）
try:
    import langextract as lx
    LANGEXTRACT_AVAILABLE = True
except Exception:
    LANGEXTRACT_AVAILABLE = False


# ------------------------- 基础工具 -------------------------

def read_file(path: str) -> str:
    with open(path, 'r', encoding='utf-8') as f:
        return f.read()


def extract_turns_from_dialogue(md_text: str) -> List[Dict[str, Any]]:
    """从对话 Markdown 提取轮次（解析 user/assistant 全量），保持顺序。"""
    blocks = re.split(r"\n---\n", md_text)
    turns = []
    turn_id = 0
    for b in blocks:
        if '## 🧑‍💻 User' in b:
            content = b.split('## 🧑‍💻 User', 1)[-1].strip()
            content = re.sub(r'^#+.*\n', '', content).strip()
            turn_id += 1
            turns.append({"turn_id": turn_id, "role": 'user', "text": content})
        elif '## 🤖 Assistant' in b:
            content = b.split('## 🤖 Assistant', 1)[-1].strip()
            content = re.sub(r'^#+.*\n', '', content).strip()
            # assistant 回合不递增 turn_id，绑定上一个 user 回合
            turns.append({"turn_id": turn_id, "role": 'assistant', "text": content})
    return [t for t in turns if t.get('text')]


def split_sentences(text: str) -> List[Dict[str, Any]]:
    """基于中文句读的简单句子切分，返回字典含句子与起止位。"""
    sentences: List[Dict[str, Any]] = []
    pattern = re.compile(r'([^。！？\n\r]+[。！？]?)')
    pos = 0
    for m in pattern.finditer(text):
        sent = m.group(1)
        if not sent:
            continue
        start = m.start(1)
        end = m.end(1)
        sentences.append({"text": sent, "start": start, "end": end})
        pos = end
    if pos < len(text):
        tail = text[pos:]
        if tail.strip():
            sentences.append({"text": tail, "start": pos, "end": len(text)})
    return sentences


def semantic_split(text: str, target_size: int = 900, max_size: int = 1200) -> List[Dict[str, Any]]:
    """仿 E2 的语义分割：按句子累加到 target_size，越界或超过 max_size 时切片。"""
    sents = split_sentences(text)
    chunks: List[Dict[str, Any]] = []
    cur: List[Dict[str, Any]] = []
    cur_len = 0
    cur_start = 0
    cid = 0
    for seg in sents:
        if not cur:
            cur_start = seg['start']
        seg_len = len(seg['text'])
        if cur_len + seg_len > target_size and cur_len > 0:
            content = ''.join([x['text'] for x in cur]).strip()
            chunks.append({
                'id': f'chunk_{cid}',
                'content': content,
                'start': cur_start,
                'end': cur_start + len(content)
            })
            cid += 1
            cur = [seg]
            cur_start = seg['start']
            cur_len = seg_len
        else:
            cur.append(seg)
            cur_len += seg_len
        if cur_len > max_size:
            content = ''.join([x['text'] for x in cur]).strip()
            chunks.append({
                'id': f'chunk_{cid}',
                'content': content,
                'start': cur_start,
                'end': cur_start + len(content)
            })
            cid += 1
            cur = []
            cur_len = 0
    if cur:
        content = ''.join([x['text'] for x in cur]).strip()
        chunks.append({
            'id': f'chunk_{cid}',
            'content': content,
            'start': cur_start,
            'end': cur_start + len(content)
        })
    return chunks


def _cosine(a, b):
    if np is None:
        return 0.0
    an = np.linalg.norm(a) + 1e-8
    bn = np.linalg.norm(b) + 1e-8
    return float(np.dot(a, b) / (an * bn))


def _embed_openai(texts: List[str], model: str) -> Optional[List[List[float]]]:
    try:
        import openai
        client = openai.OpenAI(api_key=os.getenv('OPENAI_API_KEY'), base_url=os.getenv('OPENAI_BASE_URL'))
        res = client.embeddings.create(model=model, input=texts)
        return [d.embedding for d in res.data]
    except Exception as e:
        print(f"OpenAI embedding 失败: {e}")
        return None


def _embed_local(texts: List[str]) -> Optional[List[List[float]]]:
    try:
        from sentence_transformers import SentenceTransformer
        model_name = os.getenv('LOCAL_EMBED_MODEL', 'all-MiniLM-L6-v2')
        m = SentenceTransformer(model_name)
        vecs = m.encode(texts, show_progress_bar=False)
        return [v.tolist() for v in vecs]
    except Exception as e:
        print(f"本地 embedding 失败: {e}")
        return None


def vector_retrieval_manticore(query: str, chunks: List[Dict[str, Any]], top_k: int = 3) -> List[Dict[str, Any]]:
    """使用 Manticore 官方 Python 客户端构建临时向量表并进行 KNN 检索。"""
    if not MANTICORE_CLIENT_AVAILABLE:
        raise RuntimeError('manticoresearch 客户端不可用')

    embed_model = os.getenv('EMBED_MODEL', 'text-embedding-3-small')
    texts = [c['content'] for c in chunks]
    vecs = _embed_openai(texts, embed_model) or _embed_local(texts)
    if not vecs:
        raise RuntimeError('无法生成嵌入，放弃 Manticore 路径')
    dim = len(vecs[0])

    host = os.getenv('MANTICORE_HOST', 'localhost')
    port = int(os.getenv('MANTICORE_HTTP_PORT', os.getenv('MANTICORE_PORT', '9308')))

    configuration = manticoresearch.Configuration(host=f"http://{host}:{port}")
    api_client = manticoresearch.ApiClient(configuration)
    utils_api = manticoresearch.UtilsApi(api_client)
    search_api = manticoresearch.SearchApi(api_client)
    index_api = manticoresearch.IndexApi(api_client)

    table = os.getenv('MANTICORE_TABLE', 'realistic_replay_chunks')
    # 清表并重建
    try:
        utils_api.sql(f"DROP TABLE IF EXISTS {table}")
    except Exception:
        pass
    utils_api.sql(
        f"""
        CREATE TABLE {table} (
            chunk_id string,
            start int,
            end int,
            content text,
            embedding float_vector knn_type='hnsw' knn_dims='{dim}' hnsw_similarity='cosine'
        )
        """
    )

    # 索引全部切片
    for ch, v in zip(chunks, vecs):
        doc = {
            'chunk_id': ch['id'],
            'start': int(ch['start']),
            'end': int(ch['end']),
            'content': ch['content'][:5000],
            'embedding': v
        }
        index_api.insert({'table': table, 'doc': doc})

    # 查询向量
    qv_list = _embed_openai([query], embed_model) or _embed_local([query])
    if not qv_list:
        raise RuntimeError('无法为查询生成嵌入')
    qv = qv_list[0]

    # KNN 检索
    search_request = {
        'table': table,
        'knn': {
            'field': 'embedding',
            'query_vector': qv,
            'k': top_k
        }
    }
    response = search_api.search(search_request)

    out: List[Dict[str, Any]] = []
    hits = getattr(response, 'hits', None)
    if hits:
        for hit in hits:
            src = getattr(hit, '_source', None) or {}
            # 根据 chunk_id 反查 start/end
            cid = src.get('chunk_id')
            ch = next((c for c in chunks if c['id'] == cid), None)
            if not ch:
                continue
            out.append({
                'chunk_id': ch['id'],
                'doc_id': 'source_doc',
                'offsets': [ch['start'], ch['end']],
                'score': 0.8,  # Manticore 不直接返回相似度分数，给个合理值
                'preview': ch['content'][:200]
            })

    # 如果 Manticore 没有返回结果，使用本地相似度计算作为回退
    if not out and np is not None:
        print("Manticore 未返回结果，使用本地相似度回退...")
        qv_np = np.array(qv, dtype=float)
        scored = []
        for ch, v in zip(chunks, vecs):
            s = _cosine(qv_np, np.array(v, dtype=float))
            scored.append((s, ch))
        scored.sort(key=lambda x: x[0], reverse=True)

        for s, ch in scored[:top_k]:
            out.append({
                'chunk_id': ch['id'],
                'doc_id': 'source_doc',
                'offsets': [ch['start'], ch['end']],
                'score': float(s),
                'preview': ch['content'][:200]
            })

    return out


def call_llm(prompt: str, model: str = None) -> str:
    """调用聚合 LLM（OpenAI 兼容协议）。若不可用，返回占位生成。"""
    try:
        import openai
        client = openai.OpenAI(
            api_key=os.getenv('OPENAI_API_KEY'),
            base_url=os.getenv('OPENAI_BASE_URL')
        )
        model = model or os.getenv('MODEL', 'gpt-5-nano')
        resp = client.chat.completions.create(
            model=model,
            messages=[{"role": "user", "content": prompt}],
            temperature=0.3,
        )
        return resp.choices[0].message.content
    except Exception as e:
        return f"[LLM调用失败: {e}] 无法生成回答。"


def simulate_user_response(ai_response: str, conversation_context: str, original_user_intent: str) -> str:
    """模拟用户基于AI回答的自然回复"""
    prompt = f"""你是一个真实的用户，正在与AI导师进行学习对话。

对话背景：{conversation_context}
你的学习意图：{original_user_intent}

AI刚才回答了：
{ai_response}

请作为用户，基于AI的回答进行自然的回复。你的回复应该：
1. 体现对AI回答的理解或困惑
2. 提出进一步的问题或要求澄清
3. 表达你的学习需求和兴趣点
4. 保持自然的对话风格，不要过于正式

请直接给出用户的回复，不要加任何前缀："""

    return call_llm(prompt, model=os.getenv('INTENT_MODEL', os.getenv('MODEL', 'gpt-5-nano')))


# ------------------------- 主流程 -------------------------

def run_realistic_replay(args):
    """运行真实对话重放"""
    print("开始真实对话重放...")
    
    # 读取原始文档
    doc_candidates = glob.glob(os.path.join(args.corpus_dir, "*大脑健身房*解读*.md"))
    if not doc_candidates:
        raise FileNotFoundError("未找到原始文档（匹配 *大脑健身房*解读*.md）")
    source_doc_path = doc_candidates[0]
    source_text = read_file(source_doc_path)
    print(f"✓ 加载源文档: {os.path.basename(source_doc_path)}")

    # 读取对话记录（仅用于获取初始用户意图）
    dlg_candidates = glob.glob(os.path.join(args.corpus_dir, "大脑健身入门指导*.md"))
    if not dlg_candidates:
        raise FileNotFoundError("未找到对话记录（匹配 大脑健身入门指导*.md）")
    dialogue_path = dlg_candidates[0]
    dialogue_text = read_file(dialogue_path)
    print(f"✓ 加载对话记录: {os.path.basename(dialogue_path)}")

    # 切分文档（语义分割）
    chunks = semantic_split(source_text, target_size=args.target_size, max_size=args.max_size)
    print(f"✓ 文档切分完成: {len(chunks)} 个切片")

    # 读取 persona
    persona_path = os.path.join(args.corpus_dir, 'persona-prompt.md')
    persona_text = read_file(persona_path) if os.path.exists(persona_path) else ''
    print(f"✓ 加载 Persona: {'是' if persona_text else '否'}")

    # 解析原始对话，获取第一个用户问题作为起始意图
    all_turns = extract_turns_from_dialogue(dialogue_text)
    user_turns = [t for t in all_turns if t['role'] == 'user']
    if not user_turns:
        raise ValueError("未找到用户轮次")
    
    initial_user_question = user_turns[0]['text']
    print(f"✓ 初始用户问题: {initial_user_question[:50]}...")

    # 运行目录（避免覆盖）
    ts = time.strftime('%Y%m%d-%H%M%S')
    default_run_name = f"realistic-{os.getenv('MODEL', 'gpt-5-nano')}-{ts}"
    safe_run_name = (args.run_name or default_run_name).replace('/', '-').replace(' ', '-')
    run_dir = os.path.join(RESULTS_DIR, 'runs', safe_run_name)
    os.makedirs(run_dir, exist_ok=True)
    print(f"✓ 运行目录: {run_dir}")

    # 会话结果目录
    session_id = f"realistic_replay_{ts}"
    session_dir = os.path.join(run_dir, 'sessions', session_id)
    os.makedirs(session_dir, exist_ok=True)

    # 报告头部元信息
    report_lines = [
        f"# 真实对话重放报告 - {session_id}",
        "",
        "## 运行信息",
        f"- Run name: {safe_run_name}",
        f"- Timestamp: {ts}",
        f"- Model: {os.getenv('MODEL', 'gpt-5-nano')}",
        f"- Intent model: {os.getenv('INTENT_MODEL', os.getenv('MODEL', 'gpt-5-nano'))}",
        f"- Embed model: {os.getenv('EMBED_MODEL', 'text-embedding-3-small')}",
        f"- Search engine: manticore (真实)",
        f"- 源文档: {os.path.basename(source_doc_path)}",
        f"- 初始问题来源: {os.path.basename(dialogue_path)}",
        ""
    ]

    # 开始真实对话循环
    conversation_history: List[Dict[str, str]] = []
    current_user_input = initial_user_question
    conversation_context = "用户想要学习《大脑健身房》相关内容"
    
    for round_num in range(1, args.max_rounds + 1):
        print(f"\n=== Round {round_num} ===")
        print(f"用户输入: {current_user_input}")
        
        try:
            # 1. 向量检索
            print("执行向量检索...")
            retrieved = vector_retrieval_manticore(current_user_input, chunks, top_k=args.top_k)
            print(f"✓ 检索到 {len(retrieved)} 个相关切片")
            
            if not retrieved:
                print("⚠️ 检索未返回结果，可能存在问题")
                if args.fail_fast:
                    print("启用快速失败，停止重放")
                    break
            
            # 2. 拼装上下文
            stitched_context = "\n\n".join([r['preview'] for r in retrieved])
            
            # 3. 组装短期记忆（最近 N 轮）
            recent = conversation_history[-args.max_history:]
            mem_text = "\n".join([f"{h['role']}: {h['content']}" for h in recent]) if recent else ""
            
            # 4. 构建 Prompt
            prompt_parts = []
            if persona_text:
                prompt_parts.append(f"[Persona]\n{persona_text.strip()}\n")
            if mem_text:
                prompt_parts.append(f"[Conversation Memory]\n{mem_text}\n")
            prompt_parts.append(f"[Materials]\n{stitched_context}\n")
            prompt_parts.append(f"[Question]\n{current_user_input}\n")
            prompt_parts.append("要求：基于材料与记忆，用简洁中文回答，必要时指出依据来自文档片段或常识。")
            prompt = "\n".join(prompt_parts)
            
            # 5. 调用 LLM 生成回答
            print("生成AI回答...")
            ai_response = call_llm(prompt)
            print(f"✓ AI回答: {ai_response[:100]}...")
            
            if "[LLM调用失败" in ai_response:
                print("⚠️ LLM调用失败")
                if args.fail_fast:
                    print("启用快速失败，停止重放")
                    break
            
            # 6. 保存轮次结果
            round_obj = {
                "round_num": round_num,
                "user_input": current_user_input,
                "retrieved_chunks": retrieved,
                "stitched_ctx_tokens": len(stitched_context),
                "ai_response": ai_response,
                "timestamp": time.time()
            }
            
            round_path = os.path.join(session_dir, f"round_{round_num:03d}.json")
            with open(round_path, 'w', encoding='utf-8') as f:
                json.dump(round_obj, f, ensure_ascii=False, indent=2)
            
            # 7. 更新对话历史
            conversation_history.append({"role": "user", "content": current_user_input})
            conversation_history.append({"role": "assistant", "content": ai_response})
            
            # 8. 更新报告
            report_lines.append(f"## Round {round_num}")
            report_lines.append(f"- User: {current_user_input[:100]}")
            report_lines.append(f"- Retrieved: {len(retrieved)} chunks")
            report_lines.append(f"- AI: {ai_response[:150].replace(chr(10), ' ')}")
            report_lines.append("")
            
            # 9. 如果不是最后一轮，生成下一轮用户输入
            if round_num < args.max_rounds:
                print("生成下一轮用户回复...")
                next_user_input = simulate_user_response(ai_response, conversation_context, initial_user_question)
                print(f"✓ 下一轮用户输入: {next_user_input[:100]}...")
                
                if "[LLM调用失败" in next_user_input:
                    print("⚠️ 用户回复生成失败")
                    if args.fail_fast:
                        print("启用快速失败，停止重放")
                        break
                
                current_user_input = next_user_input
            
        except Exception as e:
            print(f"❌ Round {round_num} 执行失败: {e}")
            if args.fail_fast:
                print("启用快速失败，停止重放")
                break
            else:
                # 记录错误并继续
                report_lines.append(f"## Round {round_num} (失败)")
                report_lines.append(f"- Error: {str(e)}")
                report_lines.append("")

    # 写入最终报告
    report_path = os.path.join(run_dir, 'realistic_replay_report.md')
    with open(report_path, 'w', encoding='utf-8') as f:
        f.write("\n".join(report_lines))
    
    print(f"\n✓ 真实对话重放完成")
    print(f"✓ 会话目录: {session_dir}")
    print(f"✓ 报告: {report_path}")


def build_arg_parser() -> argparse.ArgumentParser:
    p = argparse.ArgumentParser(description='真实对话重放（Realistic Replay）')
    p.add_argument('--corpus_dir', default=DEFAULT_CORPUS_DIR, help='语料目录（原始文档与对话）')
    p.add_argument('--target_size', type=int, default=900, help='语义分割目标长度')
    p.add_argument('--max_size', type=int, default=1200, help='语义分割最大长度')
    p.add_argument('--top_k', type=int, default=3, help='检索 TopK')
    p.add_argument('--max_rounds', type=int, default=6, help='最多对话轮数')
    p.add_argument('--max_history', type=int, default=2, help='短期记忆窗口（最近轮数）')
    p.add_argument('--run_name', type=str, default=None, help='运行名（默认 realistic-MODEL-时间戳）')
    p.add_argument('--fail_fast', action='store_true', help='遇到错误立即停止（默认继续）')
    return p


if __name__ == '__main__':
    parser = build_arg_parser()
    args = parser.parse_args()
    run_realistic_replay(args)
